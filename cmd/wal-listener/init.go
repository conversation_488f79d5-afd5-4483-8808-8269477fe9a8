package main

import (
	"context"
	"fmt"
	"log/slog"
	"strings"
	"net"

	"github.com/jackc/pgx"
	"github.com/nats-io/nats.go"

	"github.com/ihippik/wal-listener/v2/internal/config"
	"github.com/ihippik/wal-listener/v2/internal/publisher"
)

func normalizeCloudSQLHost(h string) (string, bool) {
	if strings.Count(h, ":") == 2 && !strings.HasPrefix(h, "/cloudsql/") {
		return "/cloudsql/" + h, true
	}
	return h, strings.HasPrefix(h, "/cloudsql/")
}

func isPrivateIPv4(ip string) bool {
	parsed := net.ParseIP(ip)
	if parsed == nil {
		return false
	}
	privateBlocks := []string{"10.0.0.0/8", "**********/12", "***********/16"}
	for _, cidr := range privateBlocks {
		_, block, _ := net.ParseCIDR(cidr)
		if block.Contains(parsed) {
			return true
		}
	}
	return false
}

func hintForDBErr(host string, isSocket bool, err error) string {
	var b strings.Builder
	fmt.Fprintf(&b, "db connect failed; host=%q socket=%t. ", host, isSocket)

	// Timeouts usually = wrong networking path
	if strings.Contains(err.Error(), "dial tcp") && strings.Contains(err.Error(), "i/o timeout") ||
		strings.Contains(err.Error(), "connect: connection timed out") {
		if isSocket {
			b.WriteString("Using Cloud SQL socket but still timed out. Check: --add-cloudsql-instances on this revision, runtime SA has roles/cloudsql.client in DB project.")
		} else if isPrivateIPv4(host) {
			b.WriteString("Looks like a private IP. Attach a Serverless VPC Connector in same region and set egress=all-traffic; allow connector CIDR to tcp:5432 in firewall.")
		} else {
			b.WriteString("TCP timeout. Verify host/port reachable from Cloud Run; for Cloud SQL prefer Unix socket at /cloudsql/<project:region:instance>.")
		}
	}

	// Common permission issues
	if strings.Contains(err.Error(), "no pg_hba.conf entry") {
		b.WriteString(" pg_hba.conf rejected the connection; allow the connector source or use Cloud SQL socket auth.")
	}
	if strings.Contains(err.Error(), "password authentication failed") {
		b.WriteString(" bad username/password; verify DB_USER/DB_PASSWORD.")
	}
	if strings.Contains(err.Error(), "permission denied to create replication slot") {
		b.WriteString(" replication user lacks REPLICATION privilege; grant or use a user with REPLICATION.")
	}
	return b.String()
}
// initPgxConnections initialise db and replication connections.
func initPgxConnections(cfg *config.DatabaseCfg, logger *slog.Logger) (*pgx.Conn, *pgx.ReplicationConn, error) {

   if strings.Count(cfg.Host, ":") == 2 && !strings.HasPrefix(cfg.Host, "/cloudsql/") {
        cfg.Host = "/cloudsql/" + cfg.Host
    }

    isSocket := strings.HasPrefix(cfg.Host, "/")
	logger.Error("connecting to database", "host", cfg.Host, "port", cfg.Port, "db", cfg.Name, "user", cfg.User,"isSocket", isSocket)



	pgxConf := pgx.ConnConfig{
        LogLevel: pgx.LogLevelInfo,
        Logger:   pgxLogger{logger},
        Host:     cfg.Host,
        Port:     cfg.Port,       // still fine for sockets; selects the socket filename
        Database: cfg.Name,
        User:     cfg.User,
        Password: cfg.Password,
		OnNotice: func(c *pgx.Conn, n *pgx.Notice) {
			const codeFeatureNotSupported = "0A000"
			// ERROR: logical decoding cannot be used while in recovery (SQLSTATE 0A000)
			if n.Code == codeFeatureNotSupported {
				logger.Error("on notice handler: received error code 0A000, closing connection", "notice", n)
				// close connection to properly prepare for exit
				if err := c.Close(); err != nil {
					logger.Error("on notice handler: unable to close connection", "err", err)
				}

				panic("on notice handler: received error code 0A000")
			}

			logger.Debug("on notice handler: received notice message", "notice", n)
		},
	}

	pgConn, err := pgx.Connect(pgxConf)
	if err != nil {
	    return nil, nil, fmt.Errorf("%w: %s", err, hintForDBErr(cfg.Host, isSocket, err))
	}


	rConnection, err := pgx.ReplicationConnect(pgxConf)
	if err != nil {
		return nil, nil, fmt.Errorf("replication connect: %w", err)
	}

	return pgConn, rConnection, nil
}

type pgxLogger struct {
	logger *slog.Logger
}

// Log DB message.
func (l pgxLogger) Log(_ pgx.LogLevel, msg string, _ map[string]any) {
	l.logger.Debug(msg)
}

type eventPublisher interface {
	Publish(context.Context, string, *publisher.Event) error
	Close() error
}

// factoryPublisher represents a factory function for creating an eventPublisher.
func factoryPublisher(ctx context.Context, cfg *config.PublisherCfg, logger *slog.Logger) (eventPublisher, error) {
	switch cfg.Type {
	case config.PublisherTypeKafka:
		producer, err := publisher.NewProducer(cfg)
		if err != nil {
			return nil, fmt.Errorf("kafka producer: %w", err)
		}

		return publisher.NewKafkaPublisher(cfg, logger, producer), nil
	case config.PublisherTypeNats:
		conn, err := nats.Connect(cfg.Address)
		if err != nil {
			return nil, fmt.Errorf("nats connection: %w", err)
		}

		pub, err := publisher.NewNatsPublisher(conn, logger)
		if err != nil {
			return nil, fmt.Errorf("new nats publisher: %w", err)
		}

		if err := pub.CreateStream(cfg.Topic); err != nil {
			return nil, fmt.Errorf("create stream: %w", err)
		}

		return pub, nil
	case config.PublisherTypeRabbitMQ:
		conn, err := publisher.NewConnection(cfg)
		if err != nil {
			return nil, fmt.Errorf("new connection: %w", err)
		}

		p, err := publisher.NewPublisher(cfg.Topic, conn)
		if err != nil {
			return nil, fmt.Errorf("new publisher: %w", err)
		}

		pub, err := publisher.NewRabbitPublisher(cfg.Topic, conn, p)
		if err != nil {
			return nil, fmt.Errorf("new rabbit publisher: %w", err)
		}

		return pub, nil
	case config.PublisherTypeGooglePubSub:
		pubSubConn, err := publisher.NewPubSubConnection(ctx, logger, cfg.PubSubProjectID)
		if err != nil {
			return nil, fmt.Errorf("could not create pubsub connection: %w", err)
		}

		return publisher.NewGooglePubSubPublisher(pubSubConn), nil
	default:
		return nil, fmt.Errorf("unknown publisher type: %s", cfg.Type)
	}
}
