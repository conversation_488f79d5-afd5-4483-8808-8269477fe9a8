// Code generated by mockery; DO NOT EDIT.
// github.com/vektra/mockery
// template: testify

package listener

import (
	"context"

	"github.com/ihippik/wal-listener/v2/internal/listener/transaction"
	"github.com/ihippik/wal-listener/v2/internal/publisher"
	"github.com/jackc/pgx"
	mock "github.com/stretchr/testify/mock"
)

// newMockeventPublisher creates a new instance of mockeventPublisher. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func newMockeventPublisher(t interface {
	mock.TestingT
	Cleanup(func())
}) *mockeventPublisher {
	mock := &mockeventPublisher{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// mockeventPublisher is an autogenerated mock type for the eventPublisher type
type mockeventPublisher struct {
	mock.Mock
}

type mockeventPublisher_Expecter struct {
	mock *mock.Mock
}

func (_m *mockeventPublisher) EXPECT() *mockeventPublisher_Expecter {
	return &mockeventPublisher_Expecter{mock: &_m.Mock}
}

// Publish provides a mock function for the type mockeventPublisher
func (_mock *mockeventPublisher) Publish(context1 context.Context, s string, event *publisher.Event) error {
	ret := _mock.Called(context1, s, event)

	if len(ret) == 0 {
		panic("no return value specified for Publish")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, string, *publisher.Event) error); ok {
		r0 = returnFunc(context1, s, event)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// mockeventPublisher_Publish_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Publish'
type mockeventPublisher_Publish_Call struct {
	*mock.Call
}

// Publish is a helper method to define mock.On call
//   - context1 context.Context
//   - s string
//   - event *publisher.Event
func (_e *mockeventPublisher_Expecter) Publish(context1 interface{}, s interface{}, event interface{}) *mockeventPublisher_Publish_Call {
	return &mockeventPublisher_Publish_Call{Call: _e.mock.On("Publish", context1, s, event)}
}

func (_c *mockeventPublisher_Publish_Call) Run(run func(context1 context.Context, s string, event *publisher.Event)) *mockeventPublisher_Publish_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 string
		if args[1] != nil {
			arg1 = args[1].(string)
		}
		var arg2 *publisher.Event
		if args[2] != nil {
			arg2 = args[2].(*publisher.Event)
		}
		run(
			arg0,
			arg1,
			arg2,
		)
	})
	return _c
}

func (_c *mockeventPublisher_Publish_Call) Return(err error) *mockeventPublisher_Publish_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *mockeventPublisher_Publish_Call) RunAndReturn(run func(context1 context.Context, s string, event *publisher.Event) error) *mockeventPublisher_Publish_Call {
	_c.Call.Return(run)
	return _c
}

// newMockparser creates a new instance of mockparser. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func newMockparser(t interface {
	mock.TestingT
	Cleanup(func())
}) *mockparser {
	mock := &mockparser{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// mockparser is an autogenerated mock type for the parser type
type mockparser struct {
	mock.Mock
}

type mockparser_Expecter struct {
	mock *mock.Mock
}

func (_m *mockparser) EXPECT() *mockparser_Expecter {
	return &mockparser_Expecter{mock: &_m.Mock}
}

// ParseWalMessage provides a mock function for the type mockparser
func (_mock *mockparser) ParseWalMessage(bytes []byte, wAL *transaction.WAL) error {
	ret := _mock.Called(bytes, wAL)

	if len(ret) == 0 {
		panic("no return value specified for ParseWalMessage")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func([]byte, *transaction.WAL) error); ok {
		r0 = returnFunc(bytes, wAL)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// mockparser_ParseWalMessage_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ParseWalMessage'
type mockparser_ParseWalMessage_Call struct {
	*mock.Call
}

// ParseWalMessage is a helper method to define mock.On call
//   - bytes []byte
//   - wAL *transaction.WAL
func (_e *mockparser_Expecter) ParseWalMessage(bytes interface{}, wAL interface{}) *mockparser_ParseWalMessage_Call {
	return &mockparser_ParseWalMessage_Call{Call: _e.mock.On("ParseWalMessage", bytes, wAL)}
}

func (_c *mockparser_ParseWalMessage_Call) Run(run func(bytes []byte, wAL *transaction.WAL)) *mockparser_ParseWalMessage_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 []byte
		if args[0] != nil {
			arg0 = args[0].([]byte)
		}
		var arg1 *transaction.WAL
		if args[1] != nil {
			arg1 = args[1].(*transaction.WAL)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *mockparser_ParseWalMessage_Call) Return(err error) *mockparser_ParseWalMessage_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *mockparser_ParseWalMessage_Call) RunAndReturn(run func(bytes []byte, wAL *transaction.WAL) error) *mockparser_ParseWalMessage_Call {
	_c.Call.Return(run)
	return _c
}

// newMockreplication creates a new instance of mockreplication. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func newMockreplication(t interface {
	mock.TestingT
	Cleanup(func())
}) *mockreplication {
	mock := &mockreplication{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// mockreplication is an autogenerated mock type for the replication type
type mockreplication struct {
	mock.Mock
}

type mockreplication_Expecter struct {
	mock *mock.Mock
}

func (_m *mockreplication) EXPECT() *mockreplication_Expecter {
	return &mockreplication_Expecter{mock: &_m.Mock}
}

// Close provides a mock function for the type mockreplication
func (_mock *mockreplication) Close() error {
	ret := _mock.Called()

	if len(ret) == 0 {
		panic("no return value specified for Close")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func() error); ok {
		r0 = returnFunc()
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// mockreplication_Close_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Close'
type mockreplication_Close_Call struct {
	*mock.Call
}

// Close is a helper method to define mock.On call
func (_e *mockreplication_Expecter) Close() *mockreplication_Close_Call {
	return &mockreplication_Close_Call{Call: _e.mock.On("Close")}
}

func (_c *mockreplication_Close_Call) Run(run func()) *mockreplication_Close_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *mockreplication_Close_Call) Return(err error) *mockreplication_Close_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *mockreplication_Close_Call) RunAndReturn(run func() error) *mockreplication_Close_Call {
	_c.Call.Return(run)
	return _c
}

// CreateReplicationSlotEx provides a mock function for the type mockreplication
func (_mock *mockreplication) CreateReplicationSlotEx(slotName string, outputPlugin string) (string, string, error) {
	ret := _mock.Called(slotName, outputPlugin)

	if len(ret) == 0 {
		panic("no return value specified for CreateReplicationSlotEx")
	}

	var r0 string
	var r1 string
	var r2 error
	if returnFunc, ok := ret.Get(0).(func(string, string) (string, string, error)); ok {
		return returnFunc(slotName, outputPlugin)
	}
	if returnFunc, ok := ret.Get(0).(func(string, string) string); ok {
		r0 = returnFunc(slotName, outputPlugin)
	} else {
		r0 = ret.Get(0).(string)
	}
	if returnFunc, ok := ret.Get(1).(func(string, string) string); ok {
		r1 = returnFunc(slotName, outputPlugin)
	} else {
		r1 = ret.Get(1).(string)
	}
	if returnFunc, ok := ret.Get(2).(func(string, string) error); ok {
		r2 = returnFunc(slotName, outputPlugin)
	} else {
		r2 = ret.Error(2)
	}
	return r0, r1, r2
}

// mockreplication_CreateReplicationSlotEx_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateReplicationSlotEx'
type mockreplication_CreateReplicationSlotEx_Call struct {
	*mock.Call
}

// CreateReplicationSlotEx is a helper method to define mock.On call
//   - slotName string
//   - outputPlugin string
func (_e *mockreplication_Expecter) CreateReplicationSlotEx(slotName interface{}, outputPlugin interface{}) *mockreplication_CreateReplicationSlotEx_Call {
	return &mockreplication_CreateReplicationSlotEx_Call{Call: _e.mock.On("CreateReplicationSlotEx", slotName, outputPlugin)}
}

func (_c *mockreplication_CreateReplicationSlotEx_Call) Run(run func(slotName string, outputPlugin string)) *mockreplication_CreateReplicationSlotEx_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 string
		if args[0] != nil {
			arg0 = args[0].(string)
		}
		var arg1 string
		if args[1] != nil {
			arg1 = args[1].(string)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *mockreplication_CreateReplicationSlotEx_Call) Return(consistentPoint string, snapshotName string, err error) *mockreplication_CreateReplicationSlotEx_Call {
	_c.Call.Return(consistentPoint, snapshotName, err)
	return _c
}

func (_c *mockreplication_CreateReplicationSlotEx_Call) RunAndReturn(run func(slotName string, outputPlugin string) (string, string, error)) *mockreplication_CreateReplicationSlotEx_Call {
	_c.Call.Return(run)
	return _c
}

// DropReplicationSlot provides a mock function for the type mockreplication
func (_mock *mockreplication) DropReplicationSlot(slotName string) error {
	ret := _mock.Called(slotName)

	if len(ret) == 0 {
		panic("no return value specified for DropReplicationSlot")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(string) error); ok {
		r0 = returnFunc(slotName)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// mockreplication_DropReplicationSlot_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DropReplicationSlot'
type mockreplication_DropReplicationSlot_Call struct {
	*mock.Call
}

// DropReplicationSlot is a helper method to define mock.On call
//   - slotName string
func (_e *mockreplication_Expecter) DropReplicationSlot(slotName interface{}) *mockreplication_DropReplicationSlot_Call {
	return &mockreplication_DropReplicationSlot_Call{Call: _e.mock.On("DropReplicationSlot", slotName)}
}

func (_c *mockreplication_DropReplicationSlot_Call) Run(run func(slotName string)) *mockreplication_DropReplicationSlot_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 string
		if args[0] != nil {
			arg0 = args[0].(string)
		}
		run(
			arg0,
		)
	})
	return _c
}

func (_c *mockreplication_DropReplicationSlot_Call) Return(err error) *mockreplication_DropReplicationSlot_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *mockreplication_DropReplicationSlot_Call) RunAndReturn(run func(slotName string) error) *mockreplication_DropReplicationSlot_Call {
	_c.Call.Return(run)
	return _c
}

// IsAlive provides a mock function for the type mockreplication
func (_mock *mockreplication) IsAlive() bool {
	ret := _mock.Called()

	if len(ret) == 0 {
		panic("no return value specified for IsAlive")
	}

	var r0 bool
	if returnFunc, ok := ret.Get(0).(func() bool); ok {
		r0 = returnFunc()
	} else {
		r0 = ret.Get(0).(bool)
	}
	return r0
}

// mockreplication_IsAlive_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'IsAlive'
type mockreplication_IsAlive_Call struct {
	*mock.Call
}

// IsAlive is a helper method to define mock.On call
func (_e *mockreplication_Expecter) IsAlive() *mockreplication_IsAlive_Call {
	return &mockreplication_IsAlive_Call{Call: _e.mock.On("IsAlive")}
}

func (_c *mockreplication_IsAlive_Call) Run(run func()) *mockreplication_IsAlive_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *mockreplication_IsAlive_Call) Return(b bool) *mockreplication_IsAlive_Call {
	_c.Call.Return(b)
	return _c
}

func (_c *mockreplication_IsAlive_Call) RunAndReturn(run func() bool) *mockreplication_IsAlive_Call {
	_c.Call.Return(run)
	return _c
}

// SendStandbyStatus provides a mock function for the type mockreplication
func (_mock *mockreplication) SendStandbyStatus(k *pgx.StandbyStatus) error {
	ret := _mock.Called(k)

	if len(ret) == 0 {
		panic("no return value specified for SendStandbyStatus")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(*pgx.StandbyStatus) error); ok {
		r0 = returnFunc(k)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// mockreplication_SendStandbyStatus_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SendStandbyStatus'
type mockreplication_SendStandbyStatus_Call struct {
	*mock.Call
}

// SendStandbyStatus is a helper method to define mock.On call
//   - k *pgx.StandbyStatus
func (_e *mockreplication_Expecter) SendStandbyStatus(k interface{}) *mockreplication_SendStandbyStatus_Call {
	return &mockreplication_SendStandbyStatus_Call{Call: _e.mock.On("SendStandbyStatus", k)}
}

func (_c *mockreplication_SendStandbyStatus_Call) Run(run func(k *pgx.StandbyStatus)) *mockreplication_SendStandbyStatus_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 *pgx.StandbyStatus
		if args[0] != nil {
			arg0 = args[0].(*pgx.StandbyStatus)
		}
		run(
			arg0,
		)
	})
	return _c
}

func (_c *mockreplication_SendStandbyStatus_Call) Return(err error) *mockreplication_SendStandbyStatus_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *mockreplication_SendStandbyStatus_Call) RunAndReturn(run func(k *pgx.StandbyStatus) error) *mockreplication_SendStandbyStatus_Call {
	_c.Call.Return(run)
	return _c
}

// StartReplication provides a mock function for the type mockreplication
func (_mock *mockreplication) StartReplication(slotName string, startLsn uint64, timeline int64, pluginArguments ...string) error {
	var tmpRet mock.Arguments
	if len(pluginArguments) > 0 {
		tmpRet = _mock.Called(slotName, startLsn, timeline, pluginArguments)
	} else {
		tmpRet = _mock.Called(slotName, startLsn, timeline)
	}
	ret := tmpRet

	if len(ret) == 0 {
		panic("no return value specified for StartReplication")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(string, uint64, int64, ...string) error); ok {
		r0 = returnFunc(slotName, startLsn, timeline, pluginArguments...)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// mockreplication_StartReplication_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'StartReplication'
type mockreplication_StartReplication_Call struct {
	*mock.Call
}

// StartReplication is a helper method to define mock.On call
//   - slotName string
//   - startLsn uint64
//   - timeline int64
//   - pluginArguments ...string
func (_e *mockreplication_Expecter) StartReplication(slotName interface{}, startLsn interface{}, timeline interface{}, pluginArguments ...interface{}) *mockreplication_StartReplication_Call {
	return &mockreplication_StartReplication_Call{Call: _e.mock.On("StartReplication",
		append([]interface{}{slotName, startLsn, timeline}, pluginArguments...)...)}
}

func (_c *mockreplication_StartReplication_Call) Run(run func(slotName string, startLsn uint64, timeline int64, pluginArguments ...string)) *mockreplication_StartReplication_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 string
		if args[0] != nil {
			arg0 = args[0].(string)
		}
		var arg1 uint64
		if args[1] != nil {
			arg1 = args[1].(uint64)
		}
		var arg2 int64
		if args[2] != nil {
			arg2 = args[2].(int64)
		}
		var arg3 []string
		var variadicArgs []string
		if len(args) > 3 {
			variadicArgs = args[3].([]string)
		}
		arg3 = variadicArgs
		run(
			arg0,
			arg1,
			arg2,
			arg3...,
		)
	})
	return _c
}

func (_c *mockreplication_StartReplication_Call) Return(err error) *mockreplication_StartReplication_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *mockreplication_StartReplication_Call) RunAndReturn(run func(slotName string, startLsn uint64, timeline int64, pluginArguments ...string) error) *mockreplication_StartReplication_Call {
	_c.Call.Return(run)
	return _c
}

// WaitForReplicationMessage provides a mock function for the type mockreplication
func (_mock *mockreplication) WaitForReplicationMessage(ctx context.Context) (*pgx.ReplicationMessage, error) {
	ret := _mock.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for WaitForReplicationMessage")
	}

	var r0 *pgx.ReplicationMessage
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context) (*pgx.ReplicationMessage, error)); ok {
		return returnFunc(ctx)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context) *pgx.ReplicationMessage); ok {
		r0 = returnFunc(ctx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*pgx.ReplicationMessage)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = returnFunc(ctx)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// mockreplication_WaitForReplicationMessage_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'WaitForReplicationMessage'
type mockreplication_WaitForReplicationMessage_Call struct {
	*mock.Call
}

// WaitForReplicationMessage is a helper method to define mock.On call
//   - ctx context.Context
func (_e *mockreplication_Expecter) WaitForReplicationMessage(ctx interface{}) *mockreplication_WaitForReplicationMessage_Call {
	return &mockreplication_WaitForReplicationMessage_Call{Call: _e.mock.On("WaitForReplicationMessage", ctx)}
}

func (_c *mockreplication_WaitForReplicationMessage_Call) Run(run func(ctx context.Context)) *mockreplication_WaitForReplicationMessage_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		run(
			arg0,
		)
	})
	return _c
}

func (_c *mockreplication_WaitForReplicationMessage_Call) Return(replicationMessage *pgx.ReplicationMessage, err error) *mockreplication_WaitForReplicationMessage_Call {
	_c.Call.Return(replicationMessage, err)
	return _c
}

func (_c *mockreplication_WaitForReplicationMessage_Call) RunAndReturn(run func(ctx context.Context) (*pgx.ReplicationMessage, error)) *mockreplication_WaitForReplicationMessage_Call {
	_c.Call.Return(run)
	return _c
}

// newMockrepository creates a new instance of mockrepository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func newMockrepository(t interface {
	mock.TestingT
	Cleanup(func())
}) *mockrepository {
	mock := &mockrepository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// mockrepository is an autogenerated mock type for the repository type
type mockrepository struct {
	mock.Mock
}

type mockrepository_Expecter struct {
	mock *mock.Mock
}

func (_m *mockrepository) EXPECT() *mockrepository_Expecter {
	return &mockrepository_Expecter{mock: &_m.Mock}
}

// Close provides a mock function for the type mockrepository
func (_mock *mockrepository) Close() error {
	ret := _mock.Called()

	if len(ret) == 0 {
		panic("no return value specified for Close")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func() error); ok {
		r0 = returnFunc()
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// mockrepository_Close_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Close'
type mockrepository_Close_Call struct {
	*mock.Call
}

// Close is a helper method to define mock.On call
func (_e *mockrepository_Expecter) Close() *mockrepository_Close_Call {
	return &mockrepository_Close_Call{Call: _e.mock.On("Close")}
}

func (_c *mockrepository_Close_Call) Run(run func()) *mockrepository_Close_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *mockrepository_Close_Call) Return(err error) *mockrepository_Close_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *mockrepository_Close_Call) RunAndReturn(run func() error) *mockrepository_Close_Call {
	_c.Call.Return(run)
	return _c
}

// CreatePublication provides a mock function for the type mockrepository
func (_mock *mockrepository) CreatePublication(ctx context.Context, name string) error {
	ret := _mock.Called(ctx, name)

	if len(ret) == 0 {
		panic("no return value specified for CreatePublication")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, string) error); ok {
		r0 = returnFunc(ctx, name)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// mockrepository_CreatePublication_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreatePublication'
type mockrepository_CreatePublication_Call struct {
	*mock.Call
}

// CreatePublication is a helper method to define mock.On call
//   - ctx context.Context
//   - name string
func (_e *mockrepository_Expecter) CreatePublication(ctx interface{}, name interface{}) *mockrepository_CreatePublication_Call {
	return &mockrepository_CreatePublication_Call{Call: _e.mock.On("CreatePublication", ctx, name)}
}

func (_c *mockrepository_CreatePublication_Call) Run(run func(ctx context.Context, name string)) *mockrepository_CreatePublication_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 string
		if args[1] != nil {
			arg1 = args[1].(string)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *mockrepository_CreatePublication_Call) Return(err error) *mockrepository_CreatePublication_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *mockrepository_CreatePublication_Call) RunAndReturn(run func(ctx context.Context, name string) error) *mockrepository_CreatePublication_Call {
	_c.Call.Return(run)
	return _c
}

// GetSlotLSN provides a mock function for the type mockrepository
func (_mock *mockrepository) GetSlotLSN(ctx context.Context, slotName string) (string, error) {
	ret := _mock.Called(ctx, slotName)

	if len(ret) == 0 {
		panic("no return value specified for GetSlotLSN")
	}

	var r0 string
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, string) (string, error)); ok {
		return returnFunc(ctx, slotName)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, string) string); ok {
		r0 = returnFunc(ctx, slotName)
	} else {
		r0 = ret.Get(0).(string)
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = returnFunc(ctx, slotName)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// mockrepository_GetSlotLSN_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetSlotLSN'
type mockrepository_GetSlotLSN_Call struct {
	*mock.Call
}

// GetSlotLSN is a helper method to define mock.On call
//   - ctx context.Context
//   - slotName string
func (_e *mockrepository_Expecter) GetSlotLSN(ctx interface{}, slotName interface{}) *mockrepository_GetSlotLSN_Call {
	return &mockrepository_GetSlotLSN_Call{Call: _e.mock.On("GetSlotLSN", ctx, slotName)}
}

func (_c *mockrepository_GetSlotLSN_Call) Run(run func(ctx context.Context, slotName string)) *mockrepository_GetSlotLSN_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 string
		if args[1] != nil {
			arg1 = args[1].(string)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *mockrepository_GetSlotLSN_Call) Return(s string, err error) *mockrepository_GetSlotLSN_Call {
	_c.Call.Return(s, err)
	return _c
}

func (_c *mockrepository_GetSlotLSN_Call) RunAndReturn(run func(ctx context.Context, slotName string) (string, error)) *mockrepository_GetSlotLSN_Call {
	_c.Call.Return(run)
	return _c
}

// IsAlive provides a mock function for the type mockrepository
func (_mock *mockrepository) IsAlive() bool {
	ret := _mock.Called()

	if len(ret) == 0 {
		panic("no return value specified for IsAlive")
	}

	var r0 bool
	if returnFunc, ok := ret.Get(0).(func() bool); ok {
		r0 = returnFunc()
	} else {
		r0 = ret.Get(0).(bool)
	}
	return r0
}

// mockrepository_IsAlive_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'IsAlive'
type mockrepository_IsAlive_Call struct {
	*mock.Call
}

// IsAlive is a helper method to define mock.On call
func (_e *mockrepository_Expecter) IsAlive() *mockrepository_IsAlive_Call {
	return &mockrepository_IsAlive_Call{Call: _e.mock.On("IsAlive")}
}

func (_c *mockrepository_IsAlive_Call) Run(run func()) *mockrepository_IsAlive_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *mockrepository_IsAlive_Call) Return(b bool) *mockrepository_IsAlive_Call {
	_c.Call.Return(b)
	return _c
}

func (_c *mockrepository_IsAlive_Call) RunAndReturn(run func() bool) *mockrepository_IsAlive_Call {
	_c.Call.Return(run)
	return _c
}

// IsReplicationActive provides a mock function for the type mockrepository
func (_mock *mockrepository) IsReplicationActive(ctx context.Context, slotName string) (bool, error) {
	ret := _mock.Called(ctx, slotName)

	if len(ret) == 0 {
		panic("no return value specified for IsReplicationActive")
	}

	var r0 bool
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(context.Context, string) (bool, error)); ok {
		return returnFunc(ctx, slotName)
	}
	if returnFunc, ok := ret.Get(0).(func(context.Context, string) bool); ok {
		r0 = returnFunc(ctx, slotName)
	} else {
		r0 = ret.Get(0).(bool)
	}
	if returnFunc, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = returnFunc(ctx, slotName)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// mockrepository_IsReplicationActive_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'IsReplicationActive'
type mockrepository_IsReplicationActive_Call struct {
	*mock.Call
}

// IsReplicationActive is a helper method to define mock.On call
//   - ctx context.Context
//   - slotName string
func (_e *mockrepository_Expecter) IsReplicationActive(ctx interface{}, slotName interface{}) *mockrepository_IsReplicationActive_Call {
	return &mockrepository_IsReplicationActive_Call{Call: _e.mock.On("IsReplicationActive", ctx, slotName)}
}

func (_c *mockrepository_IsReplicationActive_Call) Run(run func(ctx context.Context, slotName string)) *mockrepository_IsReplicationActive_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 string
		if args[1] != nil {
			arg1 = args[1].(string)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *mockrepository_IsReplicationActive_Call) Return(b bool, err error) *mockrepository_IsReplicationActive_Call {
	_c.Call.Return(b, err)
	return _c
}

func (_c *mockrepository_IsReplicationActive_Call) RunAndReturn(run func(ctx context.Context, slotName string) (bool, error)) *mockrepository_IsReplicationActive_Call {
	_c.Call.Return(run)
	return _c
}

// NewStandbyStatus provides a mock function for the type mockrepository
func (_mock *mockrepository) NewStandbyStatus(walPositions ...uint64) (*pgx.StandbyStatus, error) {
	var tmpRet mock.Arguments
	if len(walPositions) > 0 {
		tmpRet = _mock.Called(walPositions)
	} else {
		tmpRet = _mock.Called()
	}
	ret := tmpRet

	if len(ret) == 0 {
		panic("no return value specified for NewStandbyStatus")
	}

	var r0 *pgx.StandbyStatus
	var r1 error
	if returnFunc, ok := ret.Get(0).(func(...uint64) (*pgx.StandbyStatus, error)); ok {
		return returnFunc(walPositions...)
	}
	if returnFunc, ok := ret.Get(0).(func(...uint64) *pgx.StandbyStatus); ok {
		r0 = returnFunc(walPositions...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*pgx.StandbyStatus)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(...uint64) error); ok {
		r1 = returnFunc(walPositions...)
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// mockrepository_NewStandbyStatus_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'NewStandbyStatus'
type mockrepository_NewStandbyStatus_Call struct {
	*mock.Call
}

// NewStandbyStatus is a helper method to define mock.On call
//   - walPositions ...uint64
func (_e *mockrepository_Expecter) NewStandbyStatus(walPositions ...interface{}) *mockrepository_NewStandbyStatus_Call {
	return &mockrepository_NewStandbyStatus_Call{Call: _e.mock.On("NewStandbyStatus",
		append([]interface{}{}, walPositions...)...)}
}

func (_c *mockrepository_NewStandbyStatus_Call) Run(run func(walPositions ...uint64)) *mockrepository_NewStandbyStatus_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 []uint64
		var variadicArgs []uint64
		if len(args) > 0 {
			variadicArgs = args[0].([]uint64)
		}
		arg0 = variadicArgs
		run(
			arg0...,
		)
	})
	return _c
}

func (_c *mockrepository_NewStandbyStatus_Call) Return(status *pgx.StandbyStatus, err error) *mockrepository_NewStandbyStatus_Call {
	_c.Call.Return(status, err)
	return _c
}

func (_c *mockrepository_NewStandbyStatus_Call) RunAndReturn(run func(walPositions ...uint64) (*pgx.StandbyStatus, error)) *mockrepository_NewStandbyStatus_Call {
	_c.Call.Return(run)
	return _c
}

// newMockmonitor creates a new instance of mockmonitor. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func newMockmonitor(t interface {
	mock.TestingT
	Cleanup(func())
}) *mockmonitor {
	mock := &mockmonitor{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// mockmonitor is an autogenerated mock type for the monitor type
type mockmonitor struct {
	mock.Mock
}

type mockmonitor_Expecter struct {
	mock *mock.Mock
}

func (_m *mockmonitor) EXPECT() *mockmonitor_Expecter {
	return &mockmonitor_Expecter{mock: &_m.Mock}
}

// IncFilterSkippedEvents provides a mock function for the type mockmonitor
func (_mock *mockmonitor) IncFilterSkippedEvents(table string) {
	_mock.Called(table)
	return
}

// mockmonitor_IncFilterSkippedEvents_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'IncFilterSkippedEvents'
type mockmonitor_IncFilterSkippedEvents_Call struct {
	*mock.Call
}

// IncFilterSkippedEvents is a helper method to define mock.On call
//   - table string
func (_e *mockmonitor_Expecter) IncFilterSkippedEvents(table interface{}) *mockmonitor_IncFilterSkippedEvents_Call {
	return &mockmonitor_IncFilterSkippedEvents_Call{Call: _e.mock.On("IncFilterSkippedEvents", table)}
}

func (_c *mockmonitor_IncFilterSkippedEvents_Call) Run(run func(table string)) *mockmonitor_IncFilterSkippedEvents_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 string
		if args[0] != nil {
			arg0 = args[0].(string)
		}
		run(
			arg0,
		)
	})
	return _c
}

func (_c *mockmonitor_IncFilterSkippedEvents_Call) Return() *mockmonitor_IncFilterSkippedEvents_Call {
	_c.Call.Return()
	return _c
}

func (_c *mockmonitor_IncFilterSkippedEvents_Call) RunAndReturn(run func(table string)) *mockmonitor_IncFilterSkippedEvents_Call {
	_c.Run(run)
	return _c
}

// IncProblematicEvents provides a mock function for the type mockmonitor
func (_mock *mockmonitor) IncProblematicEvents(kind string) {
	_mock.Called(kind)
	return
}

// mockmonitor_IncProblematicEvents_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'IncProblematicEvents'
type mockmonitor_IncProblematicEvents_Call struct {
	*mock.Call
}

// IncProblematicEvents is a helper method to define mock.On call
//   - kind string
func (_e *mockmonitor_Expecter) IncProblematicEvents(kind interface{}) *mockmonitor_IncProblematicEvents_Call {
	return &mockmonitor_IncProblematicEvents_Call{Call: _e.mock.On("IncProblematicEvents", kind)}
}

func (_c *mockmonitor_IncProblematicEvents_Call) Run(run func(kind string)) *mockmonitor_IncProblematicEvents_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 string
		if args[0] != nil {
			arg0 = args[0].(string)
		}
		run(
			arg0,
		)
	})
	return _c
}

func (_c *mockmonitor_IncProblematicEvents_Call) Return() *mockmonitor_IncProblematicEvents_Call {
	_c.Call.Return()
	return _c
}

func (_c *mockmonitor_IncProblematicEvents_Call) RunAndReturn(run func(kind string)) *mockmonitor_IncProblematicEvents_Call {
	_c.Run(run)
	return _c
}

// IncPublishedEvents provides a mock function for the type mockmonitor
func (_mock *mockmonitor) IncPublishedEvents(subject string, table string) {
	_mock.Called(subject, table)
	return
}

// mockmonitor_IncPublishedEvents_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'IncPublishedEvents'
type mockmonitor_IncPublishedEvents_Call struct {
	*mock.Call
}

// IncPublishedEvents is a helper method to define mock.On call
//   - subject string
//   - table string
func (_e *mockmonitor_Expecter) IncPublishedEvents(subject interface{}, table interface{}) *mockmonitor_IncPublishedEvents_Call {
	return &mockmonitor_IncPublishedEvents_Call{Call: _e.mock.On("IncPublishedEvents", subject, table)}
}

func (_c *mockmonitor_IncPublishedEvents_Call) Run(run func(subject string, table string)) *mockmonitor_IncPublishedEvents_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 string
		if args[0] != nil {
			arg0 = args[0].(string)
		}
		var arg1 string
		if args[1] != nil {
			arg1 = args[1].(string)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *mockmonitor_IncPublishedEvents_Call) Return() *mockmonitor_IncPublishedEvents_Call {
	_c.Call.Return()
	return _c
}

func (_c *mockmonitor_IncPublishedEvents_Call) RunAndReturn(run func(subject string, table string)) *mockmonitor_IncPublishedEvents_Call {
	_c.Run(run)
	return _c
}
