package publisher

import (
	"context"
	"fmt"
	"log/slog"
	"sync"

	"cloud.google.com/go/pubsub"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
)

// PubSubConnection represent Pub/Sub connection.
type PubSubConnection struct {
	logger    *slog.Logger
	client    *pubsub.Client
	projectID string
	topics    map[string]*pubsub.Topic
	mu        sync.RWMutex
}

// NewPubSubConnection create new connection with specified project id.
func NewPubSubConnection(ctx context.Context, logger *slog.Logger, pubSubProjectID string) (*PubSubConnection, error) {
	// 👇 Log what we got (helps catch empty/misspelled cfg keys)
	logger.Info("pubsub: initializing connection", "projectID", pubSubProjectID)

	if pubSubProjectID == "" {
		logger.Error("pubsub: missing project id for connection")
		return nil, fmt.Errorf("project id is required for pub sub connection")
	}

	cli, err := pubsub.NewClient(ctx, pubSubProjectID)
	if err != nil {
		logger.Error("pubsub: failed creating client", "projectID", pubSubProjectID, "err", err)
		return nil, fmt.Errorf("new pub sub client: %w", err)
	}

	logger.Info("pubsub: client created", "projectID", pubSubProjectID)

	return &PubSubConnection{
		logger:    logger,
		client:    cli,
		projectID: pubSubProjectID,
		topics:    make(map[string]*pubsub.Topic),
	}, nil
}

func (c *PubSubConnection) getTopic(topic string) *pubsub.Topic {
	c.mu.Lock()
	defer c.mu.Unlock()

	if top, ok := c.topics[topic]; ok {
		// noisy only on debug; shows cache hit
		c.logger.Debug("pubsub: topic cache hit", "projectID", c.projectID, "topic", topic)
		return top
	}

	c.logger.Info("pubsub: topic cache miss (creating handle)", "projectID", c.projectID, "topic", topic)
	t := c.client.TopicInProject(topic, c.projectID)
	t.PublishSettings.NumGoroutines = 1
	t.PublishSettings.CountThreshold = 1
	c.topics[topic] = t

	return t
}

func (c *PubSubConnection) Publish(ctx context.Context, topic string, data []byte) error {
	c.logger.Debug("pubsub: publish", "projectID", c.projectID, "topic", topic, "bytes", len(data))
	t := c.getTopic(topic)
	defer t.Flush()

	res := t.Publish(ctx, &pubsub.Message{Data: data})

	if _, err := res.Get(ctx); err != nil {
		c.logger.Error("pubsub: failed to publish message", "projectID", c.projectID, "topic", topic, "err", err)

		if status.Code(err) == codes.NotFound {
			return fmt.Errorf("topic not found %w", err)
		}
		return fmt.Errorf("get: %w", err)
	}

	c.logger.Debug("pubsub: publish ok", "projectID", c.projectID, "topic", topic)
	return nil
}

func (c *PubSubConnection) Close() error {
	c.logger.Info("pubsub: closing client", "projectID", c.projectID)
	return c.client.Close()
}
