version: '3.9'

volumes:
    postgresql:

x-postgres-common:
  &postgres-common
  image: postgres:14-alpine
  user: postgres
  restart: always
  healthcheck:
    test: 'pg_isready -U postgres --dbname=my_db'
    interval: 10s
    timeout: 5s
    retries: 5

services:
  postgres_primary:
    <<: *postgres-common
    ports:
      - 5432:5432
    environment:
      POSTGRES_USER: postgres
      POSTGRES_DB: postgres
      POSTGRES_PASSWORD: postgres
      POSTGRES_HOST_AUTH_METHOD: "scram-sha-256\nhost replication all 0.0.0.0/0 md5"
      POSTGRES_INITDB_ARGS: "--auth-host=scram-sha-256"
    command: |
      postgres 
      -c wal_level=logical
      -c hot_standby=on 
      -c max_wal_senders=10 
      -c max_replication_slots=10 
      -c hot_standby_feedback=on
    volumes:
      - ./scripts:/docker-entrypoint-initdb.d

  postgres_replica:
    <<: *postgres-common
    ports:
      - 5433:5432
    environment:
      PGUSER: replicator
      PGPASSWORD: replicator_password
    command: |
      bash -c "
      until pg_basebackup --pgdata=/var/lib/postgresql/data -R --slot=replication_slot --host=postgres_primary --port=5432
      do
      echo 'Waiting for primary to connect...'
      sleep 1s
      done
      echo 'Backup done, starting replica...'
      chmod 0700 /var/lib/postgresql/data
      postgres
      "
    depends_on:
      - postgres_primary

  rabbitmq:
    image: rabbitmq:3.9.12-management-alpine
    restart: unless-stopped
    hostname: rabbitmq
    container_name: rabbitmq-broker
    ports:
      - 5672:5672
      - 15672:15672
    healthcheck:
      test:
        [ "CMD", "echo", "Awaiting rabbitmq..." ]
      interval: 10s
      timeout: 10s
      retries: 8

  wal:
    container_name: wal
    build:
        context: ../.
        target: prod
        dockerfile: ./Dockerfile
    volumes:
        - ./certs:/certs
        - ./config.yml:/app/config.yml
    depends_on:
        - postgres_primary
        - rabbitmq
#    command: tail -f /dev/null
