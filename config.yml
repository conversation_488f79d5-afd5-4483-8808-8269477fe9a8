listener:
  slotName: external_matches
  heartbeatInterval: 10s
  refreshConnection: 30s
  filter:
    tables:
      external_deals_input:
        - insert
        - update
        - delete
  topicsMap:
    external_deals_input: matches-cdc
    public_external_deals_input: matches-cdc
logger:
  level: info
  fmt: json

database:
  host: 127.0.0.1
  port: 5432
  name: external_matches
  user: wal_user
  password: ""
  debug: false

publisher:
  type: google_pubsub
  projectID: dealcircle001
  PUBSUBPROJECTID: dealcircle001
  topic: matches-cdc

monitoring:
  promAddr: ":8080"
