# Dependencies Stage
FROM golang:1.25.0-alpine AS base
LABEL maintainer="<PERSON> <<EMAIL>>"

WORKDIR /listener
COPY go.mod go.sum ./
RUN go mod download

# Build Stage
FROM base AS build

WORKDIR /listener
COPY . .

RUN CGO_ENABLED=0 GOOS=linux go build -o app ./cmd/wal-listener

# Final Stage
FROM cgr.dev/chainguard/busybox:latest-glibc as prod


COPY --from=build /listener/app /app/
COPY config.yml /app/config.yml
WORKDIR /app/

CMD /app/app
