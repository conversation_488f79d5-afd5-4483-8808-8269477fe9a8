stages:
  - build
  - staging
  - deploy

variables:
  REGION: "europe-west3"
  SERVICE_NAME: "wal-listener"
  AR_REPO: "wal"              # Artifact Registry repo (create once)
  IMAGE_NAME: "wal-listener"
  PORT: "8080"

build_image:
  stage: build
  image: docker:27
  services:
    - name: docker:27-dind
      command:
        - "--tls=false"
        - "--registry-mirror=https://mirror.gcr.io"
  variables:
    DOCKER_TLS_CERTDIR: ""
  rules:
    - if: '$CI_COMMIT_BRANCH == "main"'
      when: always
  script:
    - set -euo pipefail
    - test -f Dockerfile || (echo "Dockerfile missing" && exit 1)
    - test -f config.yml || (echo "config.yml missing" && exit 1)

    - |
      if [[ -n "${CI_COMMIT_TAG:-}" ]]; then
        TAG="$CI_COMMIT_TAG"
      elif [[ -n "${CI_COMMIT_SHORT_SHA:-}" ]]; then
        TAG="$CI_COMMIT_SHORT_SHA"
      else
        TAG="manual-$(date +%Y%m%d%H%M%S)"
      fi
      IMAGE_URI="${REGION}-docker.pkg.dev/${PROJECT_ID}/${AR_REPO}/${IMAGE_NAME}:${TAG}"
      echo "TAG=$TAG"
    - echo "IMAGE_URI=$IMAGE_URI" | tee build.env

    # Login, build, quick check for /app/config.yml, push
    - echo "$GCP_SA_KEY" > ar-key.json
    - docker login -u _json_key -p "$(cat ar-key.json)" "https://${REGION}-docker.pkg.dev"
    - docker build -t "$IMAGE_URI" .
    - docker run --rm "$IMAGE_URI" sh -lc 'echo "ls /app:"; ls -la /app; echo; head -n 40 /app/config.yml || true'
    - docker push "$IMAGE_URI"
  artifacts:
    reports:
      dotenv: build.env
    expire_in: 1 day

qa_deploy_cloud_run:
  stage: staging
  image: gcr.io/google.com/cloudsdktool/cloud-sdk:slim
  needs:
    - job: build_image
      artifacts: true
  rules:
    - if: '$CI_COMMIT_BRANCH == "main"'
      when: always
  script:
    - set -euo pipefail
    - echo "$GCP_SA_KEY" > gcp-key.json
    - gcloud auth activate-service-account --key-file=gcp-key.json
    - gcloud config set project "$PROJECT_ID"

    # Generate valid YAML env file
    - |
      cat > envlist.yaml <<EOF
      WAL_DATABASE_HOST: "/cloudsql/${CLOUDSQL_INSTANCE_QA}"
      WAL_DATABASE_PORT: "5432"
      WAL_DATABASE_NAME: "${DB_NAME}"
      WAL_DATABASE_USER: "${DB_USER}"
      WAL_DATABASE_PASSWORD: "${DB_PASSWORD_QA}"
      WAL_DATABASE_SSLMODE: "disable"
      WAL_LISTENER_SLOTNAME: "${SLOT_NAME:-external_matches}"
      WAL_LISTENER_TOPICS_MAP_EXTERNAL_MATCHES: "${WAL_LISTENER_TOPICS_MAP_EXTERNAL_MATCHES_QA:-matches-cdc}"
      WAL_PUBLISHER_TYPE: "google_pubsub"
      WAL_PROJECTID: "${PROJECT_ID}"
      WAL_PUBSUBPROJECTID: "${PROJECT_ID}"
      WAL_PUBLISHER_PROJECTID: "${PROJECT_ID}"
      WAL_PUBLISHER_PUBSUBPROJECTID: "${PROJECT_ID}"
      WAL_PUBLISHER_TOPIC: "${WAL_PUBLISHER_TOPIC_QA}"
      WAL_LOGGER_LEVEL: "debug"
      WAL_LOGGER_FMT: "json"
      WAL_MONITORING_PROMADDR: ":${PORT}"
      EOF

    # Deploy to Cloud Run
    - |
      gcloud run deploy "${SERVICE_NAME}-qa" \
        --image "$IMAGE_URI" \
        --region "$REGION" \
        --platform managed \
        --no-allow-unauthenticated \
        --service-account "$SERVICE_ACCOUNT" \
        --port "$PORT" \
        --add-cloudsql-instances "$CLOUDSQL_INSTANCE_QA" \
        --env-vars-file=envlist.yaml



prod_deploy_cloud_run:
  stage: deploy
  image: gcr.io/google.com/cloudsdktool/cloud-sdk:slim
  needs:
    - job: build_image
      artifacts: true
  rules:
    - if: '$CI_COMMIT_BRANCH == "main"'
      when: always
  script:
    - set -euo pipefail
    - echo "$GCP_SA_KEY" > gcp-key.json
    - gcloud auth activate-service-account --key-file=gcp-key.json
    - gcloud config set project "$PROJECT_ID"

    - >
      ENVLIST="WAL_DATABASE_HOST=/cloudsql/${CLOUDSQL_INSTANCE},WAL_DATABASE_PORT=5432,
      WAL_DATABASE_NAME=${DB_NAME},WAL_DATABASE_USER=${DB_USER},WAL_DATABASE_PASSWORD=${DB_PASSWORD},
      WAL_DATABASE_SSLMODE=disable,WAL_LISTENER_SLOTNAME=${SLOT_NAME:-external_matches},
      WAL_LISTENER_TOPICS_MAP_EXTERNAL_MATCHES=${WAL_LISTENER_TOPICS_MAP_EXTERNAL_MATCHES:-matches-cdc},
      WAL_PUBLISHER_TYPE=google_pubsub,WAL_PROJECTID=${PROJECT_ID},WAL_PUBSUBPROJECTID=${PROJECT_ID},
      WAL_PUBLISHER_TYPE=google_pubsub,WAL_PUBLISHER_PROJECTID=${PROJECT_ID},WAL_PUBLISHER_PUBSUBPROJECTID=${PROJECT_ID},
      WAL_PUBLISHER_TOPIC=${WAL_PUBLISHER_TOPIC},WAL_LOGGER_LEVEL=debug,WAL_LOGGER_FMT=json,
      WAL_MONITORING_PROMADDR=:${PORT}"
    - ENVLIST="$(echo "$ENVLIST" | tr -d '[:space:]' | sed 's/,/,/g')"

    - |
      gcloud run deploy "$SERVICE_NAME" \
        --image "$IMAGE_URI" \
        --region "$REGION" \
        --platform managed \
        --no-allow-unauthenticated \
        --service-account "$SERVICE_ACCOUNT" \
        --port "$PORT" \
        --add-cloudsql-instances "$CLOUDSQL_INSTANCE" \
        --set-env-vars="$ENVLIST"
